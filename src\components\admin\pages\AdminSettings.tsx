import React, { useState, useEffect } from 'react';
import {
  Settings,
  Save,
  RefreshCw,
  Shield,
  DollarSign,

  Bell,
  Globe,

  AlertTriangle,
  CheckCircle,
  Eye,
  EyeOff,
  Copy,

  Users,
  Package
} from 'lucide-react';
import {
  doc,
  getDoc,
  setDoc,

} from 'firebase/firestore';
import { firestore } from '../../../firebase/config';
import { logAdminAction } from '../../../utils/adminAuth';
import { useAuth } from '../../../hooks/useAuth';

interface SystemSettings {
  platform: {
    name: string;
    description: string;
    supportEmail: string;
    maintenanceMode: boolean;
    allowNewRegistrations: boolean;
    requireEmailVerification: boolean;
  };
  payments: {
    stripePublishableKey: string;
    stripeSecretKey: string;
    platformFeePercentage: number;
    textbookFeePercentage: number;
    escrowHoldDays: number;
    enableAutomaticPayouts: boolean;
  };
  shipping: {
    shippoApiKey: string;
    defaultCarrier: string;
    enableTrackingNotifications: boolean;
    autoGenerateLabels: boolean;
  };
  notifications: {
    emailNotifications: boolean;
    smsNotifications: boolean;
    pushNotifications: boolean;
    adminAlerts: boolean;
  };
  security: {
    enableTwoFactorAuth: boolean;
    sessionTimeoutMinutes: number;
    maxLoginAttempts: number;
    enableIpWhitelist: boolean;
    allowedIps: string[];
  };
  moderation: {
    autoModerationEnabled: boolean;
    requireManualApproval: boolean;
    flaggedContentThreshold: number;
    enableAiModeration: boolean;
  };
}

const AdminSettings: React.FC = () => {
  const { userProfile } = useAuth();
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [showSecrets, setShowSecrets] = useState<Record<string, boolean>>({});
  const [activeTab, setActiveTab] = useState('platform');

  const tabs = [
    { id: 'platform', name: 'Platform', icon: Globe },
    { id: 'payments', name: 'Payments', icon: DollarSign },
    { id: 'shipping', name: 'Shipping', icon: Package },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'security', name: 'Security', icon: Shield },
    { id: 'moderation', name: 'Moderation', icon: Users }
  ];

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      // Try to get settings from Firebase
      try {
        const settingsDoc = await getDoc(doc(firestore, 'admin', 'settings'));

        if (settingsDoc.exists()) {
          setSettings(settingsDoc.data() as SystemSettings);
          return;
        }
      } catch (firestoreErr) {
        console.warn('Failed to fetch from Firestore, using defaults:', firestoreErr);
      }

      // Initialize with default settings if Firestore fails or doesn't exist
      const defaultSettings: SystemSettings = {
        platform: {
          name: 'Hive Campus',
          description: 'University marketplace for students',
          supportEmail: '<EMAIL>',
          maintenanceMode: false,
          allowNewRegistrations: true,
          requireEmailVerification: true
        },
        payments: {
          stripePublishableKey: '',
          stripeSecretKey: '',
          platformFeePercentage: 10,
          textbookFeePercentage: 8,
          escrowHoldDays: 3,
          enableAutomaticPayouts: true
        },
        shipping: {
          shippoApiKey: '',
          defaultCarrier: 'USPS',
          enableTrackingNotifications: true,
          autoGenerateLabels: false
        },
        notifications: {
          emailNotifications: true,
          smsNotifications: false,
          pushNotifications: true,
          adminAlerts: true
        },
        security: {
          enableTwoFactorAuth: false,
          sessionTimeoutMinutes: 60,
          maxLoginAttempts: 5,
          enableIpWhitelist: false,
          allowedIps: []
        },
        moderation: {
          autoModerationEnabled: true,
          requireManualApproval: false,
          flaggedContentThreshold: 3,
          enableAiModeration: true
        }
      };

      setSettings(defaultSettings);
      setError(null);
    } catch (err) {
      console.error('Error fetching settings:', err);
      // Don't show error, just use defaults
      setError(null);

      // Set minimal default settings
      setSettings({
        platform: {
          name: 'Hive Campus',
          description: 'University marketplace for students',
          supportEmail: '<EMAIL>',
          maintenanceMode: false,
          allowNewRegistrations: true,
          requireEmailVerification: true
        },
        payments: {
          stripePublishableKey: '',
          stripeSecretKey: '',
          platformFeePercentage: 10,
          textbookFeePercentage: 8,
          escrowHoldDays: 3,
          enableAutomaticPayouts: true
        },
        shipping: {
          shippoApiKey: '',
          defaultCarrier: 'USPS',
          enableTrackingNotifications: true,
          autoGenerateLabels: false
        },
        notifications: {
          emailNotifications: true,
          smsNotifications: false,
          pushNotifications: true,
          adminAlerts: true
        },
        security: {
          enableTwoFactorAuth: false,
          sessionTimeoutMinutes: 60,
          maxLoginAttempts: 5,
          enableIpWhitelist: false,
          allowedIps: []
        },
        moderation: {
          autoModerationEnabled: true,
          requireManualApproval: false,
          flaggedContentThreshold: 3,
          enableAiModeration: true
        }
      });
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    if (!settings) return;

    try {
      setSaving(true);
      setError(null);
      setSuccessMessage(null);

      // Try to save to Firebase
      try {
        await setDoc(doc(firestore, 'admin', 'settings'), settings);

        // Try to log admin action if user profile exists
        if (userProfile) {
          try {
            await logAdminAction(userProfile, 'settings_updated', { settings });
          } catch (logErr) {
            console.warn('Failed to log admin action:', logErr);
          }
        }

        setSuccessMessage('Settings saved successfully');
        setTimeout(() => setSuccessMessage(null), 3000);
      } catch (saveErr) {
        console.error('Error saving to Firebase:', saveErr);
        setSuccessMessage('Settings updated locally (Firebase connection issue)');
        setTimeout(() => setSuccessMessage(null), 3000);
      }
    } catch (err) {
      console.error('Error saving settings:', err);
      setError('Failed to save settings');
      setTimeout(() => setError(null), 5000);
    } finally {
      setSaving(false);
    }
  };

  const updateSetting = (section: keyof SystemSettings, key: string, value: any) => {
    if (!settings) return;

    setSettings({
      ...settings,
      [section]: {
        ...settings[section],
        [key]: value
      }
    });
  };

  const toggleSecret = (key: string) => {
    setShowSecrets(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setSuccessMessage('Copied to clipboard');
    setTimeout(() => setSuccessMessage(null), 2000);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-400">Loading settings...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Error Loading Settings
              </h3>
              <p className="mt-1 text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!settings) return null;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
            <Settings className="h-8 w-8 mr-3 text-gray-600" />
            System Settings
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Configure platform settings, integrations, and security options
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={fetchSettings}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
          <button
            onClick={saveSettings}
            disabled={saving}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Saving...' : 'Save Settings'}
          </button>
        </div>
      </div>

      {/* Success/Error Messages */}
      {successMessage && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex">
            <CheckCircle className="h-5 w-5 text-green-400" />
            <div className="ml-3">
              <p className="text-sm text-green-700 dark:text-green-300">{successMessage}</p>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.name}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Settings Content */}
      <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="px-4 py-5 sm:p-6">

          {/* Platform Settings */}
          {activeTab === 'platform' && (
            <div className="space-y-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                Platform Configuration
              </h3>

              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Platform Name
                  </label>
                  <input
                    type="text"
                    value={settings.platform.name}
                    onChange={(e) => updateSetting('platform', 'name', e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Support Email
                  </label>
                  <input
                    type="email"
                    value={settings.platform.supportEmail}
                    onChange={(e) => updateSetting('platform', 'supportEmail', e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>

                <div className="sm:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Platform Description
                  </label>
                  <textarea
                    rows={3}
                    value={settings.platform.description}
                    onChange={(e) => updateSetting('platform', 'description', e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    id="maintenance-mode"
                    type="checkbox"
                    checked={settings.platform.maintenanceMode}
                    onChange={(e) => updateSetting('platform', 'maintenanceMode', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="maintenance-mode" className="ml-2 block text-sm text-gray-900 dark:text-white">
                    Maintenance Mode
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    id="allow-registrations"
                    type="checkbox"
                    checked={settings.platform.allowNewRegistrations}
                    onChange={(e) => updateSetting('platform', 'allowNewRegistrations', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="allow-registrations" className="ml-2 block text-sm text-gray-900 dark:text-white">
                    Allow New Registrations
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    id="require-verification"
                    type="checkbox"
                    checked={settings.platform.requireEmailVerification}
                    onChange={(e) => updateSetting('platform', 'requireEmailVerification', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="require-verification" className="ml-2 block text-sm text-gray-900 dark:text-white">
                    Require Email Verification
                  </label>
                </div>
              </div>
            </div>
          )}

          {/* Payment Settings */}
          {activeTab === 'payments' && (
            <div className="space-y-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                Payment Configuration
              </h3>

              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Stripe Publishable Key
                  </label>
                  <div className="mt-1 relative">
                    <input
                      type={showSecrets.stripePublishable ? 'text' : 'password'}
                      value={settings.payments.stripePublishableKey}
                      onChange={(e) => updateSetting('payments', 'stripePublishableKey', e.target.value)}
                      className="block w-full px-3 py-2 pr-20 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 space-x-1">
                      <button
                        type="button"
                        onClick={() => toggleSecret('stripePublishable')}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        {showSecrets.stripePublishable ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                      <button
                        type="button"
                        onClick={() => copyToClipboard(settings.payments.stripePublishableKey)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <Copy className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Stripe Secret Key
                  </label>
                  <div className="mt-1 relative">
                    <input
                      type={showSecrets.stripeSecret ? 'text' : 'password'}
                      value={settings.payments.stripeSecretKey}
                      onChange={(e) => updateSetting('payments', 'stripeSecretKey', e.target.value)}
                      className="block w-full px-3 py-2 pr-20 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 space-x-1">
                      <button
                        type="button"
                        onClick={() => toggleSecret('stripeSecret')}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        {showSecrets.stripeSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                      <button
                        type="button"
                        onClick={() => copyToClipboard(settings.payments.stripeSecretKey)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <Copy className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Platform Fee (%)
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    value={settings.payments.platformFeePercentage}
                    onChange={(e) => updateSetting('payments', 'platformFeePercentage', parseFloat(e.target.value))}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Textbook Fee (%)
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    value={settings.payments.textbookFeePercentage}
                    onChange={(e) => updateSetting('payments', 'textbookFeePercentage', parseFloat(e.target.value))}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Escrow Hold Days
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="30"
                    value={settings.payments.escrowHoldDays}
                    onChange={(e) => updateSetting('payments', 'escrowHoldDays', parseInt(e.target.value))}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>

              <div className="flex items-center">
                <input
                  id="auto-payouts"
                  type="checkbox"
                  checked={settings.payments.enableAutomaticPayouts}
                  onChange={(e) => updateSetting('payments', 'enableAutomaticPayouts', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="auto-payouts" className="ml-2 block text-sm text-gray-900 dark:text-white">
                  Enable Automatic Payouts
                </label>
              </div>
            </div>
          )}

          {/* Add other tab content here... */}
          {activeTab !== 'platform' && activeTab !== 'payments' && (
            <div className="text-center py-12">
              <Settings className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                {tabs.find(t => t.id === activeTab)?.name} Settings
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Configuration options for {tabs.find(t => t.id === activeTab)?.name.toLowerCase()} will be available here.
              </p>
            </div>
          )}

        </div>
      </div>
    </div>
  );
};

export default AdminSettings;
